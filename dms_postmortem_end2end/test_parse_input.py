#!/usr/bin/env python3
"""
测试 parse_input 函数的解析功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, '.')

# 导入 parse_input 函数
from dms_postmortem_optimised import parse_input

def test_parse_input():
    """测试 parse_input 函数"""
    
    # 测试用例1: 引用的路径
    test_input1 = '"/home/<USER>/tool_kit/dms_postmortem_end2end/dms_video_segments_wisq688r/2025-07-11 15-13-33_000600_000630_1920_1080_0_0_000010-000020.mp4",[:]'
    
    print("🧪 测试用例1: 引用的路径")
    print(f"输入: {test_input1}")
    
    try:
        result = parse_input(test_input1)
        print(f"解析结果: {result}")
        
        if result:
            video_path, frame_range = result[0]
            print(f"视频路径: '{video_path}'")
            print(f"帧范围: {frame_range}")
            
            # 检查路径是否包含引号
            if '"' in video_path:
                print("❌ 错误: 视频路径仍包含引号!")
            else:
                print("✅ 正确: 视频路径不包含引号")
                
            # 检查文件是否存在
            if os.path.exists(video_path):
                print("✅ 正确: 视频文件存在")
            else:
                print(f"❌ 错误: 视频文件不存在: {video_path}")
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 测试用例2: 非引用的路径
    test_input2 = '/home/<USER>/tool_kit/dms_postmortem_end2end/test.mp4,[:]'
    
    print("🧪 测试用例2: 非引用的路径")
    print(f"输入: {test_input2}")
    
    try:
        result = parse_input(test_input2)
        print(f"解析结果: {result}")
        
        if result:
            video_path, frame_range = result[0]
            print(f"视频路径: '{video_path}'")
            print(f"帧范围: {frame_range}")
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")

if __name__ == "__main__":
    test_parse_input()
