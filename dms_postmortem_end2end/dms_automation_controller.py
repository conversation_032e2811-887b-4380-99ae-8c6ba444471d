#!/usr/bin/env python3
"""
DMS视频处理端到端自动化控制器
集成远程服务管理、配置确认、智能缓存、视频裁剪和DMS处理的完整流程
"""

import os
import sys
import time
import tempfile
import subprocess
from typing import List
from pathlib import Path
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, TimeElapsedColumn

# 导入我们开发的模块
from remote_service_manager import RemoteServiceManager
from config_manager import ConfigManager
from cached_video_processor import CachedVideoProcessor
# 移除复杂的日志系统，使用简单的DMS专用日志
from cleanup_manager import get_cleanup_manager
from exception_handler import get_exception_handler, with_exception_handling
from performance_monitor import get_performance_monitor

console = Console()

class DMSAutomationController:
    """DMS视频处理端到端自动化控制器"""
    
    def __init__(self, cpp_kits_dir: str, remote_config_file: str = "remote_config.json"):
        """
        初始化自动化控制器

        Args:
            cpp_kits_dir: cpp_kits目录路径
            remote_config_file: 远程服务配置文件路径
        """
        self.cpp_kits_dir = Path(cpp_kits_dir)
        self.remote_config_file = remote_config_file

        # 初始化系统组件（简化版）
        self.cleanup_manager = get_cleanup_manager()
        self.exception_handler = get_exception_handler()
        self.performance_monitor = get_performance_monitor()

        # 初始化各个管理器
        self.remote_manager = None
        self.config_manager = None
        self.cache_processor = None
        
        # 流程状态
        self.workflow_stats = {
            'start_time': None,
            'end_time': None,
            'total_video_segments': 0,
            'cache_hits': 0,
            'processing_time': 0,
            'output_files': []
        }
        
        # 注册关闭处理器
        self.exception_handler.register_shutdown_handler(self._shutdown_handler)

        console.print(f"[bold blue]🚀 DMS自动化控制器初始化[/bold blue]")
        console.print(f"[cyan]CPP Kits目录:[/cyan] {self.cpp_kits_dir}")

    def _shutdown_handler(self):
        """关闭处理器"""
        try:
            if self.remote_manager:
                self.remote_manager.close_connection()

            if self.performance_monitor:
                self.performance_monitor.stop_monitoring()

        except Exception as e:
            console.print(f"[red]关闭处理器执行失败: {e}[/red]")

    @with_exception_handling("CONTROLLER", reraise=False, default_return=False)
    def initialize_managers(self) -> bool:
        """初始化所有管理器"""
        try:
            console.print("\n[bold]📦 初始化管理器组件[/bold]")

            # 初始化远程服务管理器
            console.print("[blue]1. 初始化远程服务管理器...[/blue]")
            self.remote_manager = RemoteServiceManager(
                str(self.cpp_kits_dir),
                self.remote_config_file
            )

            # 初始化配置管理器
            console.print("[blue]2. 初始化配置管理器...[/blue]")
            self.config_manager = ConfigManager(str(self.cpp_kits_dir))

            # 初始化缓存视频处理器
            console.print("[blue]3. 初始化缓存视频处理器...[/blue]")
            self.cache_processor = CachedVideoProcessor(
                cache_dir=".dms_cache",
                max_cache_age_days=7
            )

            console.print("[green]✅ 所有管理器初始化完成[/green]")
            return True

        except Exception as e:
            console.print(f"[red]❌ 管理器初始化失败: {e}[/red]")
            return False
    
    @with_exception_handling("REMOTE_SERVICE", reraise=False, default_return=False)
    def run_remote_service_workflow(self) -> bool:
        """运行远程服务管理工作流程"""
        console.print("\n[bold blue]🌐 远程服务管理工作流程[/bold blue]")
        
        try:
            # 1. 加载远程配置
            console.print("[blue]1. 加载远程服务配置...[/blue]")
            self.remote_manager.load_remote_config()
            
            # 2. 建立SSH连接
            console.print("[blue]2. 建立SSH连接...[/blue]")
            if not self.remote_manager.connect_ssh():
                console.print("[red]❌ SSH连接失败[/red]")
                return False
            
            # 3. 检查模型文件一致性
            console.print("[blue]3. 检查模型文件一致性...[/blue]")
            consistency_results = self.remote_manager.check_model_consistency()
            
            # 4. 同步模型文件（如果需要）
            if not all(consistency_results.values()):
                console.print("[blue]4. 同步模型文件...[/blue]")
                if not self.remote_manager.sync_model_files():
                    console.print("[red]❌ 模型文件同步失败[/red]")
                    return False
            else:
                console.print("[green]4. 模型文件已是最新[/green]")
            
            # 5. 清理重复服务实例
            console.print("[blue]5. 清理重复服务实例...[/blue]")
            self.remote_manager.cleanup_duplicate_services()
            
            # 6. 检查服务状态
            console.print("[blue]6. 检查服务状态...[/blue]")
            service_running = self.remote_manager.check_service_status()
            
            # 7. 启动服务（如果需要）
            if not service_running:
                console.print("[blue]7. 启动远程服务...[/blue]")
                if not self.remote_manager.start_service():
                    console.print("[red]❌ 服务启动失败[/red]")
                    return False
            else:
                console.print("[green]7. 服务已在运行[/green]")
            
            console.print("[green]✅ 远程服务管理工作流程完成[/green]")
            return True
            
        except Exception as e:
            console.print(f"[red]❌ 远程服务管理失败: {e}[/red]")
            return False
    
    @with_exception_handling("CONFIG", reraise=False, default_return=False)
    def run_config_workflow(self) -> bool:
        """运行配置管理工作流程"""
        console.print("\n[bold blue]⚙️  配置管理工作流程[/bold blue]")
        
        try:
            # 运行配置确认工作流程（5秒超时）
            success = self.config_manager.run_config_workflow(timeout=5)
            
            if success:
                console.print("[green]✅ 配置确认完成[/green]")
                return True
            else:
                console.print("[red]❌ 配置确认失败[/red]")
                return False
                
        except Exception as e:
            console.print(f"[red]❌ 配置管理失败: {e}[/red]")
            return False
    
    def process_video_segments(self, video_file: str, time_ranges: str, roi: str = None) -> List[str]:
        """
        处理视频段（带缓存）
        
        Args:
            video_file: 视频文件路径
            time_ranges: 时间范围字符串
            roi: 感兴趣区域
            
        Returns:
            List[str]: 处理后的视频文件路径列表
        """
        console.print("\n[bold blue]🎬 视频段处理工作流程[/bold blue]")
        
        try:
            # 创建临时输出目录（在当前目录下）
            temp_output_dir = tempfile.mkdtemp(prefix="dms_video_segments_", dir=".")
            console.print(f"[cyan]临时输出目录:[/cyan] {temp_output_dir}")
            
            # 使用缓存视频处理器处理视频段
            output_files = self.cache_processor.process_video_with_cache(
                video_file, time_ranges, temp_output_dir, roi
            )
            
            # 更新统计信息
            stats = self.cache_processor.get_processing_stats()
            self.workflow_stats['cache_hits'] = stats['cache_hit_count']
            self.workflow_stats['total_video_segments'] = len(self.cache_processor.parse_time_ranges(time_ranges))
            
            if output_files:
                console.print(f"[green]✅ 视频段处理完成，生成 {len(output_files)} 个文件[/green]")
                return output_files
            else:
                console.print("[red]❌ 视频段处理失败[/red]")
                return []
                
        except Exception as e:
            console.print(f"[red]❌ 视频段处理出错: {e}[/red]")
            return []
    
    def run_dms_processing(self, video_segments: List[str]) -> List[str]:
        """
        运行DMS处理流程
        
        Args:
            video_segments: 视频段文件列表
            
        Returns:
            List[str]: 处理后的输出文件列表
        """
        console.print("\n[bold blue]🧠 DMS处理工作流程[/bold blue]")
        
        try:
            output_files = []
            
            # 创建临时输入文件列表
            temp_input_file = tempfile.mktemp(suffix=".txt", dir=".")

            with open(temp_input_file, 'w') as f:
                for video_file in video_segments:
                    # 写入视频文件路径（dms_postmortem_optimised.py的输入格式）
                    # 格式: /path/to/video.mp4,[:]
                    # 确保文件路径被正确引用，处理包含空格的文件名
                    # 使用绝对路径以避免路径解析问题
                    abs_path = os.path.abspath(video_file)
                    quoted_path = f'"{abs_path}"' if ' ' in abs_path else abs_path
                    f.write(f"{quoted_path},[:]\n")
            
            console.print(f"[cyan]输入文件列表:[/cyan] {temp_input_file}")
            console.print(f"[cyan]待处理视频段:[/cyan] {len(video_segments)} 个")
            
            # 调用dms_postmortem_optimised.py
            console.print("[blue]启动DMS处理程序...[/blue]")

            # 构建命令
            dms_script = Path("dms_postmortem_optimised.py")
            if not dms_script.exists():
                console.print(f"[red]❌ DMS处理脚本不存在: {dms_script}[/red]")
                return []

            cmd = [sys.executable, str(dms_script), temp_input_file]
            
            # 设置环境变量
            env = os.environ.copy()
            env['PYTHONPATH'] = str(self.cpp_kits_dir.parent)
            env['CPP_PROGRAM_DIR'] = str(self.cpp_kits_dir)  # 设置cpp程序目录
            
            # 执行DMS处理
            start_time = time.time()

            # 创建DMS处理日志文件
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            dms_log_file = f"dms_processing_{timestamp}.log"

            console.print("[blue]🔄 启动DMS处理进程（实时输出+日志保存）...[/blue]")
            console.print(f"[cyan]📝 日志文件:[/cyan] {dms_log_file}")

            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # 合并stderr到stdout
                text=True,
                bufsize=1,  # 行缓冲
                universal_newlines=True,
                env=env,
                cwd=str(self.cpp_kits_dir.parent)
            )

            # 实时读取、显示并保存输出
            stdout_lines = []

            # 打开日志文件用于写入
            with open(dms_log_file, 'w', encoding='utf-8') as log_file:
                # 写入日志头部信息
                log_file.write(f"=== DMS处理日志 ===\n")
                log_file.write(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                log_file.write(f"命令: {' '.join(cmd)}\n")
                log_file.write(f"工作目录: {self.cpp_kits_dir.parent}\n")
                log_file.write("=" * 50 + "\n\n")
                log_file.flush()

                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    TimeElapsedColumn(),
                    console=console
                ) as progress:
                    task = progress.add_task("DMS处理中（实时输出+日志保存）...", total=None)

                    try:
                        # 实时读取、显示并保存输出
                        while True:
                            output = process.stdout.readline()
                            if output == '' and process.poll() is not None:
                                break
                            if output:
                                output = output.strip()
                                stdout_lines.append(output)

                                # 保存到日志文件
                                log_file.write(f"{datetime.now().strftime('%H:%M:%S')} | {output}\n")
                                log_file.flush()  # 立即写入磁盘

                                # 实时显示输出（带颜色区分）
                                if '[CPP]' in output:
                                    console.print(f"[cyan]🔧 {output}[/cyan]")
                                elif '[MAIN]' in output:
                                    console.print(f"[blue]📋 {output}[/blue]")
                                elif '[ASSEMBLY]' in output:
                                    console.print(f"[green]🎬 {output}[/green]")
                                elif 'ERROR' in output.upper():
                                    console.print(f"[red]❌ {output}[/red]")
                                elif 'WARNING' in output.upper():
                                    console.print(f"[yellow]⚠️  {output}[/yellow]")
                                else:
                                    console.print(f"[white]📝 {output}[/white]")

                                # 更新进度描述
                                if len(stdout_lines) % 5 == 0:  # 每5行更新一次
                                    progress.update(task, description=f"DMS处理中... (已输出{len(stdout_lines)}行)")

                        # 等待进程完成
                        process.wait()
                        progress.update(task, completed=True)

                        # 写入日志结束信息
                        log_file.write(f"\n" + "=" * 50 + "\n")
                        log_file.write(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        log_file.write(f"进程返回码: {process.returncode}\n")
                        log_file.write(f"总输出行数: {len(stdout_lines)}\n")

                    except KeyboardInterrupt:
                        console.print("[yellow]⚠️  用户中断，终止DMS处理...[/yellow]")
                        log_file.write(f"\n{datetime.now().strftime('%H:%M:%S')} | 用户中断处理\n")
                        process.terminate()
                        process.wait()
                        process.returncode = -1
                    except Exception as e:
                        console.print(f"[red]❌ 读取DMS输出时出错: {e}[/red]")
                        log_file.write(f"\n{datetime.now().strftime('%H:%M:%S')} | 错误: {e}\n")
                        process.terminate()
                        process.wait()
                        process.returncode = -1

            processing_time = time.time() - start_time
            self.workflow_stats['processing_time'] = processing_time

            # 显示日志文件位置
            console.print(f"[cyan]📝 完整日志已保存到:[/cyan] {dms_log_file}")

            if process.returncode == 0:
                console.print(f"[green]✅ DMS处理完成 (耗时: {processing_time:.1f}s)[/green]")

                # 查找输出文件
                output_video_dir = Path("output_video")
                if output_video_dir.exists():
                    output_files = list(output_video_dir.glob("*.mp4"))
                    output_files = [str(f) for f in output_files]

                    console.print(f"[green]📁 找到 {len(output_files)} 个输出文件[/green]")
                    for i, file_path in enumerate(output_files, 1):
                        console.print(f"  {i}. {file_path}")
                else:
                    console.print("[yellow]⚠️  未找到输出目录[/yellow]")

            else:
                console.print(f"[red]❌ DMS处理失败 (返回码: {process.returncode})[/red]")
                console.print(f"[red]📝 详细错误信息请查看日志文件: {dms_log_file}[/red]")
                # 显示最后几行输出作为错误信息
                if stdout_lines:
                    error_lines = stdout_lines[-5:]  # 显示最后5行
                    console.print("[red]最后的输出信息:[/red]")
                    for line in error_lines:
                        console.print(f"[red]  {line}[/red]")
            
            # 清理临时文件
            try:
                os.remove(temp_input_file)
            except:
                pass
            
            return output_files
            
        except Exception as e:
            console.print(f"[red]❌ DMS处理出错: {e}[/red]")
            return []
    
    @with_exception_handling("WORKFLOW", reraise=False, default_return=False)
    def run_end_to_end_workflow(self, video_file: str, time_ranges: str, roi: str = None) -> bool:
        """
        运行端到端自动化工作流程
        
        Args:
            video_file: 输入视频文件
            time_ranges: 时间范围字符串
            roi: 感兴趣区域
            
        Returns:
            bool: 工作流程是否成功
        """
        self.workflow_stats['start_time'] = time.time()

        # 启动性能监控
        self.performance_monitor.start_monitoring()
        self.performance_monitor.set_baseline()

        console.print("\n" + "="*80)
        console.print("[bold green]🚀 DMS视频处理端到端自动化流程启动[/bold green]")
        console.print("="*80)
        
        try:
            # 1. 初始化管理器
            if not self.initialize_managers():
                return False
            
            # 2. 远程服务管理工作流程
            if not self.run_remote_service_workflow():
                return False
            
            # 3. 配置管理工作流程
            if not self.run_config_workflow():
                return False
            
            # 4. 视频段处理工作流程
            video_segments = self.process_video_segments(video_file, time_ranges, roi)
            if not video_segments:
                return False
            
            # 5. DMS处理工作流程
            output_files = self.run_dms_processing(video_segments)
            self.workflow_stats['output_files'] = output_files
            
            # 6. 完成统计
            self.workflow_stats['end_time'] = time.time()

            # 停止性能监控
            self.performance_monitor.stop_monitoring()

            # 显示最终总结
            self.display_final_summary()

            # 显示性能报告
            self.performance_monitor.display_performance_report()

            console.print("[green]✅ 端到端工作流程完成[/green]")
            return len(output_files) > 0

        except Exception as e:
            console.print(f"[red]❌ 端到端工作流程失败: {e}[/red]")
            return False
        finally:
            # 清理连接和资源
            try:
                if self.remote_manager:
                    self.remote_manager.close_connection()

                self.performance_monitor.stop_monitoring()

                # 执行清理
                self.cleanup_manager.cleanup_registered_files()

            except Exception as e:
                console.print(f"[yellow]清理过程中出现错误: {e}[/yellow]")
    
    def display_final_summary(self):
        """显示最终总结"""
        total_time = self.workflow_stats['end_time'] - self.workflow_stats['start_time']
        
        console.print("\n" + "="*80)
        console.print("[bold green]🎉 端到端自动化流程完成[/bold green]")
        console.print("="*80)
        
        console.print(f"[cyan]总耗时:[/cyan] {total_time:.1f} 秒")
        console.print(f"[cyan]视频段数量:[/cyan] {self.workflow_stats['total_video_segments']}")
        console.print(f"[cyan]缓存命中:[/cyan] {self.workflow_stats['cache_hits']} 次")
        console.print(f"[cyan]DMS处理时间:[/cyan] {self.workflow_stats['processing_time']:.1f} 秒")
        console.print(f"[cyan]输出文件:[/cyan] {len(self.workflow_stats['output_files'])} 个")
        
        if self.workflow_stats['cache_hits'] > 0:
            cache_hit_rate = (self.workflow_stats['cache_hits'] / self.workflow_stats['total_video_segments']) * 100
            console.print(f"[green]📊 缓存命中率: {cache_hit_rate:.1f}%[/green]")
        
        if self.workflow_stats['output_files']:
            console.print(f"\n[bold blue]📁 输出文件列表:[/bold blue]")
            for i, file_path in enumerate(self.workflow_stats['output_files'], 1):
                console.print(f"  {i}. {file_path}")
        
        console.print("\n[bold green]✅ 自动化流程执行完毕[/bold green]")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='DMS视频处理端到端自动化控制器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python dms_automation_controller.py -i video.mp4 -t "00:00:10-00:00:20;00:01:00-00:01:10" -k cpp_kits_dir
  python dms_automation_controller.py -i video.mp4 -t "00:00:10-00:00:20" -k cpp_kits_dir --roi "1920:1080:0:0"
        """
    )
    
    parser.add_argument('-i', '--input', required=True, help='输入视频文件')
    parser.add_argument('-t', '--time', required=True, help='时间范围，格式: start1-end1;start2-end2')
    parser.add_argument('-k', '--cpp-kits', required=True, help='cpp_kits目录路径')
    parser.add_argument('--roi', help='感兴趣区域，格式: width:height:x:y')
    parser.add_argument('--remote-config', default='remote_config.json', help='远程服务配置文件')
    
    args = parser.parse_args()
    
    # 验证输入文件
    if not os.path.exists(args.input):
        console.print(f"[red]❌ 输入文件不存在: {args.input}[/red]")
        return 1
    
    # 验证cpp_kits目录
    if not os.path.exists(args.cpp_kits):
        console.print(f"[red]❌ cpp_kits目录不存在: {args.cpp_kits}[/red]")
        return 1
    
    # 创建控制器并运行
    controller = DMSAutomationController(args.cpp_kits, args.remote_config)
    
    success = controller.run_end_to_end_workflow(
        args.input, 
        args.time, 
        args.roi
    )
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
