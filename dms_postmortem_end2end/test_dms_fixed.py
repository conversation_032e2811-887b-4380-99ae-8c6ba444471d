#!/usr/bin/env python3
"""
DMS自动化控制器修复验证测试脚本
用于验证修复后的文件路径处理和DMS处理功能
"""

import os
import sys
import tempfile
import subprocess
from pathlib import Path

def test_file_path_processing():
    """测试文件路径处理功能"""
    print("🧪 测试文件路径处理功能...")
    
    # 模拟包含空格的文件路径
    test_video_path = "./dms_video_segments_rgcwh_g7/2025-07-11 15-13-33_000600_000630_1920_1080_0_0_000010-000020.mp4"
    
    # 检查文件是否存在
    if not os.path.exists(test_video_path):
        print(f"❌ 测试文件不存在: {test_video_path}")
        return False
    
    # 测试绝对路径转换和引用
    abs_path = os.path.abspath(test_video_path)
    quoted_path = f'"{abs_path}"' if ' ' in abs_path else abs_path
    
    print(f"✅ 原始路径: {test_video_path}")
    print(f"✅ 绝对路径: {abs_path}")
    print(f"✅ 引用路径: {quoted_path}")
    
    # 创建临时输入文件
    temp_input_file = tempfile.mktemp(suffix=".txt", dir=".")
    with open(temp_input_file, 'w') as f:
        f.write(f"{quoted_path},[:]\n")
    
    print(f"✅ 临时输入文件创建成功: {temp_input_file}")
    
    # 验证文件内容
    with open(temp_input_file, 'r') as f:
        content = f.read()
        print(f"✅ 文件内容: {content.strip()}")
    
    # 清理
    os.remove(temp_input_file)
    return True

def test_dms_processing_direct():
    """直接测试DMS处理功能"""
    print("\n🧪 直接测试DMS处理功能...")
    
    # 检查DMS脚本是否存在
    dms_script = Path("dms_postmortem_optimised.py")
    if not dms_script.exists():
        print(f"❌ DMS处理脚本不存在: {dms_script}")
        return False
    
    # 检查CPP程序目录
    cpp_kits_dir = Path("./BYD_HKH_R_2.01.07.2025.07.08.4_x86/")
    if not cpp_kits_dir.exists():
        print(f"❌ CPP程序目录不存在: {cpp_kits_dir}")
        return False
    
    # 创建测试输入文件
    test_video_path = "./dms_video_segments_rgcwh_g7/2025-07-11 15-13-33_000600_000630_1920_1080_0_0_000010-000020.mp4"
    if not os.path.exists(test_video_path):
        print(f"❌ 测试视频文件不存在: {test_video_path}")
        return False
    
    abs_path = os.path.abspath(test_video_path)
    quoted_path = f'"{abs_path}"' if ' ' in abs_path else abs_path
    
    temp_input_file = tempfile.mktemp(suffix=".txt", dir=".")
    with open(temp_input_file, 'w') as f:
        f.write(f"{quoted_path},[:]\n")
    
    print(f"✅ 测试输入文件: {temp_input_file}")
    print(f"✅ 视频文件路径: {quoted_path}")
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONPATH'] = str(cpp_kits_dir.parent)
    env['CPP_PROGRAM_DIR'] = str(cpp_kits_dir)
    
    # 构建命令
    cmd = [sys.executable, str(dms_script), temp_input_file]
    
    print(f"✅ 执行命令: {' '.join(cmd)}")
    print(f"✅ 工作目录: {cpp_kits_dir.parent}")
    print(f"✅ CPP_PROGRAM_DIR: {env['CPP_PROGRAM_DIR']}")
    
    try:
        # 执行DMS处理（限制时间为30秒用于测试）
        print("\n🔄 开始DMS处理测试（30秒超时）...")
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            env=env,
            cwd=str(cpp_kits_dir.parent)
        )
        
        # 读取前几行输出
        output_lines = []
        for i in range(20):  # 只读取前20行
            try:
                line = process.stdout.readline()
                if line:
                    output_lines.append(line.strip())
                    print(f"📝 {line.strip()}")
                else:
                    break
            except:
                break
        
        # 终止进程
        process.terminate()
        process.wait()
        
        print(f"\n✅ DMS处理测试完成，读取了 {len(output_lines)} 行输出")
        
        # 检查是否有严重错误
        error_count = sum(1 for line in output_lines if 'ERROR' in line.upper())
        warning_count = sum(1 for line in output_lines if 'WARN' in line.upper())
        
        print(f"📊 错误数量: {error_count}")
        print(f"📊 警告数量: {warning_count}")
        
        # 清理
        os.remove(temp_input_file)
        
        return error_count == 0  # 如果没有错误则认为测试成功
        
    except Exception as e:
        print(f"❌ DMS处理测试失败: {e}")
        # 清理
        try:
            os.remove(temp_input_file)
        except:
            pass
        return False

def main():
    """主测试函数"""
    print("🚀 开始DMS自动化控制器修复验证测试")
    print("=" * 60)
    
    # 测试1: 文件路径处理
    test1_result = test_file_path_processing()
    
    # 测试2: DMS处理功能
    test2_result = test_dms_processing_direct()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"✅ 文件路径处理测试: {'通过' if test1_result else '失败'}")
    print(f"✅ DMS处理功能测试: {'通过' if test2_result else '失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！修复成功！")
        return 0
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
        return 1

if __name__ == "__main__":
    sys.exit(main())
