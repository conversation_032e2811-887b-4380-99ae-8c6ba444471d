#!/usr/bin/env python3
"""
自动化运行DMS测试脚本，绕过手动确认
"""

import subprocess
import sys
import time
import threading
import os

def run_dms_automation():
    """运行DMS自动化控制器"""
    cmd = [
        sys.executable, 
        "dms_automation_controller.py",
        "-i", "/home/<USER>/data/dms/byd/sc3e_r/250711/2025-07-11 15-13-33_000600_000630_1920_1080_0_0.mp4",
        "-t", "00:00:10-00:00:20",
        "-k", "./BYD_HKH_R_2.01.07.2025.07.08.4_x86/"
    ]
    
    print("🚀 启动DMS自动化控制器...")
    print(f"命令: {' '.join(cmd)}")
    
    process = subprocess.Popen(
        cmd,
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True,
        bufsize=1,
        universal_newlines=True
    )
    
    # 自动确认线程
    def auto_confirm():
        time.sleep(8)  # 等待8秒后自动发送确认
        try:
            process.stdin.write("\n")
            process.stdin.flush()
            print("🤖 自动发送确认信号")
        except:
            pass
    
    # 启动自动确认线程
    confirm_thread = threading.Thread(target=auto_confirm)
    confirm_thread.daemon = True
    confirm_thread.start()
    
    # 读取输出
    output_lines = []
    try:
        while True:
            line = process.stdout.readline()
            if line:
                output_lines.append(line.strip())
                print(line.strip())
                
                # 如果看到DMS处理开始，等待一段时间后终止
                if "DMS处理工作流程" in line:
                    print("🔄 DMS处理已开始，等待30秒后终止测试...")
                    time.sleep(30)
                    break
                    
                # 如果看到完成信息，正常退出
                if "处理完成" in line or "工作流程完成" in line:
                    print("✅ 处理完成！")
                    break
                    
            else:
                # 进程结束
                break
                
    except KeyboardInterrupt:
        print("🛑 用户中断")
    except Exception as e:
        print(f"❌ 读取输出时出错: {e}")
    
    # 终止进程
    try:
        process.terminate()
        process.wait(timeout=5)
    except:
        process.kill()
    
    return output_lines

def main():
    """主函数"""
    print("🧪 DMS自动化控制器端到端测试")
    print("=" * 60)
    
    # 检查必要文件
    required_files = [
        "dms_automation_controller.py",
        "BYD_HKH_R_2.01.07.2025.07.08.4_x86",
        "/home/<USER>/data/dms/byd/sc3e_r/250711/2025-07-11 15-13-33_000600_000630_1920_1080_0_0.mp4"
    ]
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            print(f"❌ 必要文件不存在: {file_path}")
            return 1
    
    print("✅ 所有必要文件检查通过")
    
    # 运行测试
    output_lines = run_dms_automation()
    
    print("\n" + "=" * 60)
    print("📊 测试结果分析:")
    
    # 分析输出
    success_indicators = [
        "远程服务管理工作流程完成",
        "配置管理工作流程完成", 
        "视频段处理工作流程完成",
        "DMS处理工作流程"
    ]
    
    completed_stages = 0
    for indicator in success_indicators:
        if any(indicator in line for line in output_lines):
            completed_stages += 1
            print(f"✅ {indicator}")
        else:
            print(f"❌ {indicator}")
    
    error_count = sum(1 for line in output_lines if 'ERROR' in line.upper() and 'GStreamer' not in line)
    warning_count = sum(1 for line in output_lines if 'WARN' in line.upper() and 'GStreamer' not in line)
    
    print(f"\n📈 统计信息:")
    print(f"   完成阶段: {completed_stages}/{len(success_indicators)}")
    print(f"   严重错误: {error_count}")
    print(f"   警告信息: {warning_count}")
    
    if completed_stages >= 3:  # 至少完成前3个阶段
        print("\n🎉 测试成功！主要功能正常工作")
        return 0
    else:
        print("\n❌ 测试失败，部分功能异常")
        return 1

if __name__ == "__main__":
    sys.exit(main())
